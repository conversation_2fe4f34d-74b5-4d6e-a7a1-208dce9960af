using System.Security.Principal;

namespace CaptureAccountCreationRegistryChange
{
    public partial class Form1 : Form
    {
        private readonly List<RegistryMonitor> _monitors = new();
        private RegistryChangeCapture? _changeCapture;
        private readonly string[] _registryHives = { "SAM", "SECURITY", "SOFTWARE" };

        public Form1()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Check if running as administrator
            if (!IsRunningAsAdministrator())
            {
                MessageBox.Show(
                    "This application requires administrator privileges to monitor SAM and SECURITY registry hives.\n\n" +
                    "Please restart the application as an administrator.",
                    "Administrator Required",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
            }

            // Set default output folder
            var defaultFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "RegistryChanges");
            txtOutputFolder.Text = defaultFolder;

            LogMessage("Application started. Ready to monitor registry changes.");
        }

        private bool IsRunningAsAdministrator()
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        private void btnStartMonitoring_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtOutputFolder.Text))
                {
                    MessageBox.Show("Please select an output folder first.", "Output Folder Required",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Create output directory if it doesn't exist
                Directory.CreateDirectory(txtOutputFolder.Text);

                // Initialize change capture
                _changeCapture = new RegistryChangeCapture(txtOutputFolder.Text);

                // Start monitoring selected hives
                var selectedHives = GetSelectedHives();
                if (selectedHives.Count == 0)
                {
                    MessageBox.Show("Please select at least one registry hive to monitor.", "No Hives Selected",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                foreach (var hive in selectedHives)
                {
                    var monitor = new RegistryMonitor(hive);
                    monitor.RegistryChanged += OnRegistryChanged;
                    monitor.StartMonitoring();
                    _monitors.Add(monitor);
                    LogMessage($"Started monitoring {hive} hive");
                }

                // Update UI
                btnStartMonitoring.Enabled = false;
                btnStopMonitoring.Enabled = true;
                txtStatus.Text = $"Monitoring {selectedHives.Count} registry hive(s)";

                LogMessage($"Registry monitoring started for {string.Join(", ", selectedHives)} hives");
                LogMessage($"Changes will be saved to: {txtOutputFolder.Text}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error starting monitoring: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogMessage($"Error starting monitoring: {ex.Message}");
            }
        }

        private void btnStopMonitoring_Click(object sender, EventArgs e)
        {
            try
            {
                // Stop all monitors
                foreach (var monitor in _monitors)
                {
                    monitor.RegistryChanged -= OnRegistryChanged;
                    monitor.Dispose();
                }
                _monitors.Clear();

                // Update UI
                btnStartMonitoring.Enabled = true;
                btnStopMonitoring.Enabled = false;
                txtStatus.Text = "Monitoring stopped";

                LogMessage("Registry monitoring stopped");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error stopping monitoring: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogMessage($"Error stopping monitoring: {ex.Message}");
            }
        }

        private void btnSelectOutputFolder_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog();
            folderDialog.Description = "Select folder to save registry changes";
            folderDialog.SelectedPath = txtOutputFolder.Text;

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtOutputFolder.Text = folderDialog.SelectedPath;
                LogMessage($"Output folder set to: {folderDialog.SelectedPath}");
            }
        }

        private List<string> GetSelectedHives()
        {
            var selected = new List<string>();

            if (chkSAM.Checked) selected.Add("SAM");
            if (chkSECURITY.Checked) selected.Add("SECURITY");
            if (chkSOFTWARE.Checked) selected.Add("SOFTWARE");

            return selected;
        }

        private void OnRegistryChanged(object? sender, RegistryChangedEventArgs e)
        {
            // Use Invoke to update UI from background thread
            if (InvokeRequired)
            {
                Invoke(new Action<object?, RegistryChangedEventArgs>(OnRegistryChanged), sender, e);
                return;
            }

            LogMessage($"Registry change detected in {e.KeyPath}: {e.Message}");

            // Capture the changes
            if (_changeCapture != null)
            {
                Task.Run(() =>
                {
                    try
                    {
                        _changeCapture.CaptureChanges(e.KeyPath);
                        Invoke(new Action(() => LogMessage($"Changes captured for {e.KeyPath}")));
                    }
                    catch (Exception ex)
                    {
                        Invoke(new Action(() => LogMessage($"Error capturing changes for {e.KeyPath}: {ex.Message}")));
                    }
                });
            }
        }

        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logEntry = $"[{timestamp}] {message}";

            lstLog.Items.Insert(0, logEntry);

            // Keep only the last 1000 entries
            while (lstLog.Items.Count > 1000)
            {
                lstLog.Items.RemoveAt(lstLog.Items.Count - 1);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Clean up monitors
            foreach (var monitor in _monitors)
            {
                monitor.Dispose();
            }
            _monitors.Clear();

            base.OnFormClosing(e);
        }
    }
}
