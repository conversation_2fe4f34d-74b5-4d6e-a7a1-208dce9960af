using Microsoft.Win32;
using System.Text;

namespace CaptureAccountCreationRegistryChange
{
    public class RegistryChangeCapture
    {
        private readonly string _outputDirectory;
        private readonly Dictionary<string, Dictionary<string, object?>> _lastSnapshots;

        public RegistryChangeCapture(string outputDirectory)
        {
            _outputDirectory = outputDirectory ?? throw new ArgumentNullException(nameof(outputDirectory));
            _lastSnapshots = new Dictionary<string, Dictionary<string, object?>>();
            
            // Ensure output directory exists
            Directory.CreateDirectory(_outputDirectory);
        }

        public void CaptureChanges(string keyPath)
        {
            try
            {
                var currentSnapshot = TakeSnapshot(keyPath);
                
                if (_lastSnapshots.TryGetValue(keyPath, out var lastSnapshot))
                {
                    var changes = CompareSnapshots(keyPath, lastSnapshot, currentSnapshot);
                    if (changes.Count > 0)
                    {
                        ExportChangesToFile(keyPath, changes);
                    }
                }
                
                _lastSnapshots[keyPath] = currentSnapshot;
            }
            catch (Exception ex)
            {
                // Log error to a file
                LogError($"Error capturing changes for {keyPath}: {ex.Message}");
            }
        }

        private Dictionary<string, object?> TakeSnapshot(string keyPath)
        {
            var snapshot = new Dictionary<string, object?>();
            
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(keyPath, false);
                if (key != null)
                {
                    // Get all value names and their data
                    foreach (string valueName in key.GetValueNames())
                    {
                        try
                        {
                            var value = key.GetValue(valueName);
                            var valueKind = key.GetValueKind(valueName);
                            snapshot[$"{keyPath}\\{valueName}"] = new RegistryValue(value, valueKind);
                        }
                        catch (Exception ex)
                        {
                            snapshot[$"{keyPath}\\{valueName}"] = new RegistryValue($"ERROR: {ex.Message}", RegistryValueKind.String);
                        }
                    }

                    // Recursively get subkeys (limited depth to avoid performance issues)
                    foreach (string subKeyName in key.GetSubKeyNames())
                    {
                        try
                        {
                            var subKeySnapshot = TakeSnapshot($"{keyPath}\\{subKeyName}");
                            foreach (var kvp in subKeySnapshot)
                            {
                                snapshot[kvp.Key] = kvp.Value;
                            }
                        }
                        catch
                        {
                            // Skip inaccessible subkeys
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error taking snapshot of {keyPath}: {ex.Message}");
            }

            return snapshot;
        }

        private List<RegistryChange> CompareSnapshots(string keyPath, Dictionary<string, object?> oldSnapshot, Dictionary<string, object?> newSnapshot)
        {
            var changes = new List<RegistryChange>();

            // Find added or modified values
            foreach (var kvp in newSnapshot)
            {
                if (!oldSnapshot.TryGetValue(kvp.Key, out var oldValue))
                {
                    // New value
                    changes.Add(new RegistryChange(kvp.Key, RegistryChangeType.Added, null, kvp.Value));
                }
                else if (!AreValuesEqual(oldValue, kvp.Value))
                {
                    // Modified value
                    changes.Add(new RegistryChange(kvp.Key, RegistryChangeType.Modified, oldValue, kvp.Value));
                }
            }

            // Find deleted values
            foreach (var kvp in oldSnapshot)
            {
                if (!newSnapshot.ContainsKey(kvp.Key))
                {
                    changes.Add(new RegistryChange(kvp.Key, RegistryChangeType.Deleted, kvp.Value, null));
                }
            }

            return changes;
        }

        private bool AreValuesEqual(object? value1, object? value2)
        {
            if (value1 is RegistryValue rv1 && value2 is RegistryValue rv2)
            {
                return rv1.Kind == rv2.Kind && Equals(rv1.Data, rv2.Data);
            }
            return Equals(value1, value2);
        }

        private void ExportChangesToFile(string keyPath, List<RegistryChange> changes)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            var fileName = $"RegistryChanges_{SanitizeFileName(keyPath)}_{timestamp}.reg";
            var filePath = Path.Combine(_outputDirectory, fileName);

            var regContent = new StringBuilder();
            regContent.AppendLine("Windows Registry Editor Version 5.00");
            regContent.AppendLine();
            regContent.AppendLine($"; Registry changes detected in: {keyPath}");
            regContent.AppendLine($"; Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            regContent.AppendLine();

            foreach (var change in changes)
            {
                regContent.AppendLine($"; {change.Type}: {change.ValuePath}");
                
                var keyName = ExtractKeyName(change.ValuePath);
                var valueName = ExtractValueName(change.ValuePath);

                regContent.AppendLine($"[HKEY_LOCAL_MACHINE\\{keyName}]");

                switch (change.Type)
                {
                    case RegistryChangeType.Added:
                    case RegistryChangeType.Modified:
                        if (change.NewValue is RegistryValue newRegValue)
                        {
                            regContent.AppendLine(FormatRegistryValue(valueName, newRegValue));
                        }
                        break;
                    case RegistryChangeType.Deleted:
                        regContent.AppendLine($"\"{valueName}\"=-");
                        break;
                }
                regContent.AppendLine();
            }

            File.WriteAllText(filePath, regContent.ToString(), Encoding.Unicode);
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        private string ExtractKeyName(string valuePath)
        {
            var lastBackslash = valuePath.LastIndexOf('\\');
            return lastBackslash > 0 ? valuePath.Substring(0, lastBackslash) : valuePath;
        }

        private string ExtractValueName(string valuePath)
        {
            var lastBackslash = valuePath.LastIndexOf('\\');
            return lastBackslash >= 0 ? valuePath.Substring(lastBackslash + 1) : valuePath;
        }

        private string FormatRegistryValue(string valueName, RegistryValue regValue)
        {
            switch (regValue.Kind)
            {
                case RegistryValueKind.String:
                    return $"\"{valueName}\"=\"{EscapeRegString(regValue.Data?.ToString() ?? "")}\"";
                case RegistryValueKind.DWord:
                    return $"\"{valueName}\"=dword:{Convert.ToUInt32(regValue.Data):x8}";
                case RegistryValueKind.QWord:
                    return $"\"{valueName}\"=qword:{Convert.ToUInt64(regValue.Data):x16}";
                case RegistryValueKind.Binary:
                    if (regValue.Data is byte[] bytes)
                    {
                        return $"\"{valueName}\"=hex:{string.Join(",", bytes.Select(b => b.ToString("x2")))}";
                    }
                    break;
                case RegistryValueKind.MultiString:
                    if (regValue.Data is string[] strings)
                    {
                        var hexData = Encoding.Unicode.GetBytes(string.Join("\0", strings) + "\0\0");
                        return $"\"{valueName}\"=hex(7):{string.Join(",", hexData.Select(b => b.ToString("x2")))}";
                    }
                    break;
            }
            return $"\"{valueName}\"=\"{EscapeRegString(regValue.Data?.ToString() ?? "")}\"";
        }

        private string EscapeRegString(string input)
        {
            return input.Replace("\\", "\\\\").Replace("\"", "\\\"");
        }

        private void LogError(string message)
        {
            try
            {
                var logFile = Path.Combine(_outputDirectory, "errors.log");
                File.AppendAllText(logFile, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}\n");
            }
            catch
            {
                // Ignore logging errors
            }
        }
    }

    public class RegistryValue
    {
        public object? Data { get; }
        public RegistryValueKind Kind { get; }

        public RegistryValue(object? data, RegistryValueKind kind)
        {
            Data = data;
            Kind = kind;
        }
    }

    public class RegistryChange
    {
        public string ValuePath { get; }
        public RegistryChangeType Type { get; }
        public object? OldValue { get; }
        public object? NewValue { get; }

        public RegistryChange(string valuePath, RegistryChangeType type, object? oldValue, object? newValue)
        {
            ValuePath = valuePath;
            Type = type;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }

    public enum RegistryChangeType
    {
        Added,
        Modified,
        Deleted
    }
}
