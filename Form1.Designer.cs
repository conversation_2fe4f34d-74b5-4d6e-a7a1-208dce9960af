﻿namespace CaptureAccountCreationRegistryChange
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private Button btnStartMonitoring;
        private Button btnStopMonitoring;
        private Button btnSelectOutputFolder;
        private TextBox txtOutputFolder;
        private Label lblOutputFolder;
        private Label lblStatus;
        private TextBox txtStatus;
        private ListBox lstLog;
        private Label lblLog;
        private CheckBox chkSAM;
        private CheckBox chkSECURITY;
        private CheckBox chkSOFTWARE;
        private Label lblHives;

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            btnStartMonitoring = new Button();
            btnStopMonitoring = new Button();
            btnSelectOutputFolder = new Button();
            txtOutputFolder = new TextBox();
            lblOutputFolder = new Label();
            lblStatus = new Label();
            txtStatus = new TextBox();
            lstLog = new ListBox();
            lblLog = new Label();
            chkSAM = new CheckBox();
            chkSECURITY = new CheckBox();
            chkSOFTWARE = new CheckBox();
            lblHives = new Label();
            SuspendLayout();
            //
            // btnStartMonitoring
            //
            btnStartMonitoring.Location = new Point(12, 12);
            btnStartMonitoring.Name = "btnStartMonitoring";
            btnStartMonitoring.Size = new Size(120, 30);
            btnStartMonitoring.TabIndex = 0;
            btnStartMonitoring.Text = "Start Monitoring";
            btnStartMonitoring.UseVisualStyleBackColor = true;
            btnStartMonitoring.Click += btnStartMonitoring_Click;
            //
            // btnStopMonitoring
            //
            btnStopMonitoring.Enabled = false;
            btnStopMonitoring.Location = new Point(138, 12);
            btnStopMonitoring.Name = "btnStopMonitoring";
            btnStopMonitoring.Size = new Size(120, 30);
            btnStopMonitoring.TabIndex = 1;
            btnStopMonitoring.Text = "Stop Monitoring";
            btnStopMonitoring.UseVisualStyleBackColor = true;
            btnStopMonitoring.Click += btnStopMonitoring_Click;
            //
            // btnSelectOutputFolder
            //
            btnSelectOutputFolder.Location = new Point(12, 90);
            btnSelectOutputFolder.Name = "btnSelectOutputFolder";
            btnSelectOutputFolder.Size = new Size(100, 23);
            btnSelectOutputFolder.TabIndex = 2;
            btnSelectOutputFolder.Text = "Select Folder";
            btnSelectOutputFolder.UseVisualStyleBackColor = true;
            btnSelectOutputFolder.Click += btnSelectOutputFolder_Click;
            //
            // txtOutputFolder
            //
            txtOutputFolder.Location = new Point(118, 90);
            txtOutputFolder.Name = "txtOutputFolder";
            txtOutputFolder.ReadOnly = true;
            txtOutputFolder.Size = new Size(400, 23);
            txtOutputFolder.TabIndex = 3;
            //
            // lblOutputFolder
            //
            lblOutputFolder.AutoSize = true;
            lblOutputFolder.Location = new Point(12, 72);
            lblOutputFolder.Name = "lblOutputFolder";
            lblOutputFolder.Size = new Size(82, 15);
            lblOutputFolder.TabIndex = 4;
            lblOutputFolder.Text = "Output Folder:";
            //
            // lblStatus
            //
            lblStatus.AutoSize = true;
            lblStatus.Location = new Point(12, 125);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(42, 15);
            lblStatus.TabIndex = 5;
            lblStatus.Text = "Status:";
            //
            // txtStatus
            //
            txtStatus.Location = new Point(60, 122);
            txtStatus.Name = "txtStatus";
            txtStatus.ReadOnly = true;
            txtStatus.Size = new Size(458, 23);
            txtStatus.TabIndex = 6;
            txtStatus.Text = "Ready";
            //
            // lstLog
            //
            lstLog.FormattingEnabled = true;
            lstLog.ItemHeight = 15;
            lstLog.Location = new Point(12, 170);
            lstLog.Name = "lstLog";
            lstLog.Size = new Size(506, 259);
            lstLog.TabIndex = 7;
            //
            // lblLog
            //
            lblLog.AutoSize = true;
            lblLog.Location = new Point(12, 152);
            lblLog.Name = "lblLog";
            lblLog.Size = new Size(28, 15);
            lblLog.TabIndex = 8;
            lblLog.Text = "Log:";
            //
            // chkSAM
            //
            chkSAM.AutoSize = true;
            chkSAM.Checked = true;
            chkSAM.CheckState = CheckState.Checked;
            chkSAM.Location = new Point(300, 16);
            chkSAM.Name = "chkSAM";
            chkSAM.Size = new Size(49, 19);
            chkSAM.TabIndex = 9;
            chkSAM.Text = "SAM";
            chkSAM.UseVisualStyleBackColor = true;
            //
            // chkSECURITY
            //
            chkSECURITY.AutoSize = true;
            chkSECURITY.Checked = true;
            chkSECURITY.CheckState = CheckState.Checked;
            chkSECURITY.Location = new Point(355, 16);
            chkSECURITY.Name = "chkSECURITY";
            chkSECURITY.Size = new Size(78, 19);
            chkSECURITY.TabIndex = 10;
            chkSECURITY.Text = "SECURITY";
            chkSECURITY.UseVisualStyleBackColor = true;
            //
            // chkSOFTWARE
            //
            chkSOFTWARE.AutoSize = true;
            chkSOFTWARE.Checked = true;
            chkSOFTWARE.CheckState = CheckState.Checked;
            chkSOFTWARE.Location = new Point(439, 16);
            chkSOFTWARE.Name = "chkSOFTWARE";
            chkSOFTWARE.Size = new Size(85, 19);
            chkSOFTWARE.TabIndex = 11;
            chkSOFTWARE.Text = "SOFTWARE";
            chkSOFTWARE.UseVisualStyleBackColor = true;
            //
            // lblHives
            //
            lblHives.AutoSize = true;
            lblHives.Location = new Point(300, 0);
            lblHives.Name = "lblHives";
            lblHives.Size = new Size(108, 15);
            lblHives.TabIndex = 12;
            lblHives.Text = "Registry Hives to Monitor:";
            //
            // Form1
            //
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(530, 441);
            Controls.Add(lblHives);
            Controls.Add(chkSOFTWARE);
            Controls.Add(chkSECURITY);
            Controls.Add(chkSAM);
            Controls.Add(lblLog);
            Controls.Add(lstLog);
            Controls.Add(txtStatus);
            Controls.Add(lblStatus);
            Controls.Add(lblOutputFolder);
            Controls.Add(txtOutputFolder);
            Controls.Add(btnSelectOutputFolder);
            Controls.Add(btnStopMonitoring);
            Controls.Add(btnStartMonitoring);
            Name = "Form1";
            Text = "Registry Change Monitor";
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
    }
}
