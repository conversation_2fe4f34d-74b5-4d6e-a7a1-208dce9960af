using Microsoft.Win32;
using System.ComponentModel;
using System.Runtime.InteropServices;

namespace CaptureAccountCreationRegistryChange
{
    public class RegistryMonitor : IDisposable
    {
        #region P/Invoke declarations

        [DllImport("advapi32.dll", SetLastError = true)]
        private static extern int RegNotifyChangeKeyValue(
            IntPtr hKey,
            bool bWatchSubtree,
            RegChangeNotifyFilter dwNotifyFilter,
            IntPtr hEvent,
            bool fAsynchronous);

        [DllImport("advapi32.dll", SetLastError = true)]
        private static extern int RegOpenKeyEx(
            IntPtr hKey,
            string subKey,
            uint options,
            int samDesired,
            out IntPtr phkResult);

        [DllImport("advapi32.dll", SetLastError = true)]
        private static extern int RegCloseKey(IntPtr hKey);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateEvent(
            IntPtr lpEventAttributes,
            bool bManualReset,
            bool bInitialState,
            string? lpName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern uint WaitForSingleObject(IntPtr hHandle, uint dwMilliseconds);

        private const uint WAIT_OBJECT_0 = 0x00000000;
        private const uint WAIT_TIMEOUT = 0x00000102;
        private const uint INFINITE = 0xFFFFFFFF;

        private static readonly IntPtr HKEY_LOCAL_MACHINE = new IntPtr(unchecked((int)0x80000002));
        private const int KEY_NOTIFY = 0x0010;
        private const int KEY_READ = 0x20019;

        [Flags]
        private enum RegChangeNotifyFilter
        {
            REG_NOTIFY_CHANGE_NAME = 0x00000001,
            REG_NOTIFY_CHANGE_ATTRIBUTES = 0x00000002,
            REG_NOTIFY_CHANGE_LAST_SET = 0x00000004,
            REG_NOTIFY_CHANGE_SECURITY = 0x00000008,
        }

        #endregion

        private readonly string _keyPath;
        private IntPtr _registryKey;
        private IntPtr _eventHandle;
        private Thread? _monitorThread;
        private bool _disposed;
        private volatile bool _monitoring;

        public event EventHandler<RegistryChangedEventArgs>? RegistryChanged;

        public RegistryMonitor(string keyPath)
        {
            _keyPath = keyPath ?? throw new ArgumentNullException(nameof(keyPath));
        }

        public bool IsMonitoring => _monitoring;

        public void StartMonitoring()
        {
            if (_monitoring)
                return;

            try
            {
                // Open the registry key
                int result = RegOpenKeyEx(
                    HKEY_LOCAL_MACHINE,
                    _keyPath,
                    0,
                    KEY_NOTIFY | KEY_READ,
                    out _registryKey);

                if (result != 0)
                {
                    throw new Win32Exception(result, $"Failed to open registry key: {_keyPath}");
                }

                // Create event handle
                _eventHandle = CreateEvent(IntPtr.Zero, false, false, null);
                if (_eventHandle == IntPtr.Zero)
                {
                    throw new Win32Exception(Marshal.GetLastWin32Error(), "Failed to create event handle");
                }

                _monitoring = true;

                // Start monitoring thread
                _monitorThread = new Thread(MonitorRegistry)
                {
                    IsBackground = true,
                    Name = $"RegistryMonitor-{_keyPath}"
                };
                _monitorThread.Start();
            }
            catch
            {
                StopMonitoring();
                throw;
            }
        }

        public void StopMonitoring()
        {
            _monitoring = false;

            if (_eventHandle != IntPtr.Zero)
            {
                CloseHandle(_eventHandle);
                _eventHandle = IntPtr.Zero;
            }

            if (_registryKey != IntPtr.Zero)
            {
                RegCloseKey(_registryKey);
                _registryKey = IntPtr.Zero;
            }

            _monitorThread?.Join(5000); // Wait up to 5 seconds for thread to finish
        }

        private void MonitorRegistry()
        {
            while (_monitoring)
            {
                try
                {
                    // Set up registry change notification
                    int result = RegNotifyChangeKeyValue(
                        _registryKey,
                        true, // Watch subtree
                        RegChangeNotifyFilter.REG_NOTIFY_CHANGE_NAME |
                        RegChangeNotifyFilter.REG_NOTIFY_CHANGE_LAST_SET |
                        RegChangeNotifyFilter.REG_NOTIFY_CHANGE_ATTRIBUTES,
                        _eventHandle,
                        true); // Asynchronous

                    if (result != 0)
                    {
                        OnRegistryChanged(new RegistryChangedEventArgs(_keyPath, $"Error setting up notification: {result}"));
                        break;
                    }

                    // Wait for change notification
                    uint waitResult = WaitForSingleObject(_eventHandle, INFINITE);

                    if (waitResult == WAIT_OBJECT_0 && _monitoring)
                    {
                        OnRegistryChanged(new RegistryChangedEventArgs(_keyPath, "Registry change detected"));
                    }
                    else if (waitResult != WAIT_TIMEOUT)
                    {
                        break; // Error or stop requested
                    }
                }
                catch (Exception ex)
                {
                    OnRegistryChanged(new RegistryChangedEventArgs(_keyPath, $"Monitoring error: {ex.Message}"));
                    break;
                }
            }
        }

        protected virtual void OnRegistryChanged(RegistryChangedEventArgs e)
        {
            RegistryChanged?.Invoke(this, e);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _disposed = true;
            }
        }
    }

    public class RegistryChangedEventArgs : EventArgs
    {
        public string KeyPath { get; }
        public string Message { get; }
        public DateTime Timestamp { get; }

        public RegistryChangedEventArgs(string keyPath, string message)
        {
            KeyPath = keyPath;
            Message = message;
            Timestamp = DateTime.Now;
        }
    }
}
