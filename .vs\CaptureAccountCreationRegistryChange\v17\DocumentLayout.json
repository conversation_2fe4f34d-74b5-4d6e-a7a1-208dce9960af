{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\Projects_Coding\\CaptureAccountCreationRegistryChange\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{289D0FAC-BEE5-40E3-88AD-16511CC33DCB}|CaptureAccountCreationRegistryChange.csproj|c:\\users\\<USER>\\documents\\projects_coding\\captureaccountcreationregistrychange\\form1.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{289D0FAC-BEE5-40E3-88AD-16511CC33DCB}|CaptureAccountCreationRegistryChange.csproj|solutionrelative:form1.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Form1.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\Projects_Coding\\CaptureAccountCreationRegistryChange\\Form1.cs", "RelativeDocumentMoniker": "Form1.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\Projects_Coding\\CaptureAccountCreationRegistryChange\\Form1.cs [Design]", "RelativeToolTip": "Form1.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T17:48:47.847Z", "EditorCaption": " [Design]"}]}]}]}