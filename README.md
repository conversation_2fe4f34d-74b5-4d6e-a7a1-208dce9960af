# Registry Change Monitor

A Windows application that monitors registry changes in the SAM, SECURITY, and SOFTWARE hives and captures those changes to files for analysis.

## Features

- **Real-time monitoring** of SAM, SECURITY, and SOFTWARE registry hives
- **Change detection** that captures what was actually modified, added, or deleted
- **Export to .REG files** for easy viewing and importing
- **User-friendly interface** with start/stop controls and real-time logging
- **Administrator privilege handling** with automatic detection and warnings

## Requirements

- Windows 10/11
- .NET 9.0 or later
- **Administrator privileges** (required for SAM and SECURITY hive access)

## How to Use

### 1. Run as Administrator
The application **must** be run as an administrator to access the SAM and SECURITY registry hives. Right-click the executable and select "Run as administrator".

### 2. Select Output Folder
- Click "Select Folder" to choose where registry change files will be saved
- Default location is a "RegistryChanges" folder on your desktop

### 3. Choose Registry Hives
Select which registry hives to monitor:
- **SAM** - Security Account Manager (user accounts, passwords)
- **SECURITY** - Security policies and settings
- **SOFTWARE** - Installed software and system settings

### 4. Start Monitoring
- Click "Start Monitoring" to begin watching for registry changes
- The application will show real-time status updates in the log

### 5. View Captured Changes
When registry changes are detected, the application will:
- Create timestamped .REG files in your output folder
- Show log entries indicating what was captured
- Include details about added, modified, or deleted registry values

## Output Files

### Registry Change Files
- **Format**: `.reg` files (Windows Registry Editor format)
- **Naming**: `RegistryChanges_[HiveName]_[Timestamp].reg`
- **Content**: Only the actual changes detected, not entire registry dumps

### Error Log
- **File**: `errors.log` in the output folder
- **Content**: Any errors encountered during monitoring or file operations

## Example Output

```reg
Windows Registry Editor Version 5.00

; Registry changes detected in: SOFTWARE\Microsoft\Windows\CurrentVersion
; Timestamp: 2024-01-15 14:30:25

; Added: SOFTWARE\Microsoft\Windows\CurrentVersion\NewValue
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion]
"NewValue"="SomeData"

; Modified: SOFTWARE\Microsoft\Windows\CurrentVersion\ExistingValue
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion]
"ExistingValue"="UpdatedData"
```

## Technical Details

### Registry Monitoring
- Uses Windows API `RegNotifyChangeKeyValue` for efficient change detection
- Monitors registry names, values, and attributes
- Runs monitoring in background threads to maintain UI responsiveness

### Change Detection
- Takes snapshots before and after changes
- Compares snapshots to identify specific modifications
- Supports all registry value types (String, DWORD, QWORD, Binary, Multi-String)

### Security Considerations
- Requires administrator privileges for system hive access
- Only reads registry data, does not modify anything
- Exports changes in standard .REG format for transparency

## Troubleshooting

### "Administrator Required" Message
- The application needs administrator privileges to access SAM and SECURITY hives
- Right-click the executable and select "Run as administrator"

### "Access Denied" Errors
- Some registry keys may be protected even with administrator access
- These errors are logged but don't stop monitoring of accessible keys

### High CPU Usage
- Registry monitoring is generally lightweight
- If experiencing issues, try monitoring fewer hives or specific subkeys

### Large Output Files
- The application only captures changes, not entire registry dumps
- File size depends on the amount of registry activity

## Use Cases

- **Security Analysis**: Monitor account creation and security policy changes
- **Software Installation Tracking**: See what registry changes software makes
- **System Troubleshooting**: Identify what changed when problems occur
- **Compliance Monitoring**: Track security-related registry modifications
- **Forensic Analysis**: Capture evidence of system changes

## Building from Source

```bash
# Clone or download the source code
# Open command prompt in the project directory
dotnet build
dotnet run
```

## License

This project is provided as-is for educational and administrative purposes. Use responsibly and in accordance with your organization's policies.
